@tailwind base;
@tailwind components;
@tailwind utilities;

/* Cinematic Enhancements */
@layer base {
  * {
    scroll-behavior: smooth;
  }

  body {
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #0f0f0f 100%);
    overflow-x: hidden;
  }
}

@layer components {
  /* Cinematic Glow Effects */
  .glow-yellow {
    box-shadow: 0 0 20px rgba(255, 255, 0, 0.3), 0 0 40px rgba(255, 255, 0, 0.1);
  }

  .glow-blue {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), 0 0 40px rgba(59, 130, 246, 0.1);
  }

  /* Cinematic Text Effects */
  .text-glow {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5), 0 0 20px rgba(255, 255, 255, 0.3);
  }

  .text-glow-yellow {
    text-shadow: 0 0 10px rgba(255, 255, 0, 0.8), 0 0 20px rgba(255, 255, 0, 0.4);
  }

  /* Cinematic Animations */
  .float-animation {
    animation: float 6s ease-in-out infinite;
  }

  .pulse-glow {
    animation: pulse-glow 3s ease-in-out infinite;
  }

  .slide-up {
    animation: slide-up 0.8s ease-out forwards;
  }

  /* Cinematic Backgrounds */
  .bg-cinematic {
    background: radial-gradient(ellipse at center, rgba(255, 255, 0, 0.1) 0%, transparent 70%),
                linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
  }

  .bg-hero-pattern {
    background-image:
      radial-gradient(circle at 20% 50%, rgba(255, 255, 0, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba(168, 85, 247, 0.1) 0%, transparent 50%);
  }
}

@layer utilities {
  /* Cinematic Transforms */
  .transform-gpu {
    transform: translateZ(0);
  }

  .perspective-1000 {
    perspective: 1000px;
  }

  .preserve-3d {
    transform-style: preserve-3d;
  }
}

/* Keyframe Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 255, 0, 0.3), 0 0 40px rgba(255, 255, 0, 0.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(255, 255, 0, 0.6), 0 0 60px rgba(255, 255, 0, 0.2);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #fbbf24, #f59e0b);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #f59e0b, #d97706);
}

body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
        "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans",
        "Helvetica Neue", sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
        monospace;
}



@layer base {
  :root {
        /* Light theme using user's color palette */
        /* 7C6C77 = hsl(315, 7%, 45%) - muted purple-gray */
        /* AAA694 = hsl(49, 12%, 62%) - warm gray */
        /* D1D0A3 = hsl(59, 33%, 73%) - light olive */
        /* 5B3000 = hsl(32, 100%, 18%) - dark brown */
        /* DFC2F2 = hsl(276, 67%, 85%) - light purple */

        --background: 59 33% 95%; /* Very light olive background */
        --foreground: 32 100% 18%; /* Dark brown text */
        --card: 59 33% 92%; /* Light olive cards */
        --card-foreground: 32 100% 18%; /* Dark brown card text */
        --popover: 59 33% 95%; /* Light olive popover */
        --popover-foreground: 32 100% 18%; /* Dark brown popover text */
        --primary: 315 7% 45%; /* Muted purple-gray primary */
        --primary-foreground: 59 33% 95%; /* Light text on primary */
        --secondary: 49 12% 62%; /* Warm gray secondary */
        --secondary-foreground: 32 100% 18%; /* Dark brown on secondary */
        --muted: 49 12% 75%; /* Lighter warm gray muted */
        --muted-foreground: 32 100% 25%; /* Slightly lighter brown for muted text */
        --accent: 276 67% 85%; /* Light purple accent */
        --accent-foreground: 32 100% 18%; /* Dark brown on accent */
        --destructive: 0 84.2% 60.2%; /* Keep red for destructive */
        --destructive-foreground: 0 0% 98%; /* White on destructive */
        --border: 49 12% 80%; /* Light warm gray borders */
        --input: 59 33% 88%; /* Light olive inputs */
        --ring: 315 7% 45%; /* Muted purple-gray focus ring */
        --chart-1: 315 7% 45%; /* Primary color for charts */
        --chart-2: 49 12% 62%; /* Secondary color for charts */
        --chart-3: 276 67% 70%; /* Accent color for charts */
        --chart-4: 32 80% 35%; /* Brown variant for charts */
        --chart-5: 59 33% 60%; /* Olive variant for charts */
        --radius: 0.5rem;
    }
  .dark {
        --background: 0 0% 3.9%;
        --foreground: 0 0% 98%;
        --card: 0 0% 3.9%;
        --card-foreground: 0 0% 98%;
        --popover: 0 0% 3.9%;
        --popover-foreground: 0 0% 98%;
        --primary: 0 0% 98%;
        --primary-foreground: 0 0% 9%;
        --secondary: 0 0% 14.9%;
        --secondary-foreground: 0 0% 98%;
        --muted: 0 0% 14.9%;
        --muted-foreground: 0 0% 63.9%;
        --accent: 0 0% 14.9%;
        --accent-foreground: 0 0% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 14.9%;
        --input: 0 0% 14.9%;
        --ring: 0 0% 83.1%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
    }
}



@layer base {
  * {
    @apply border-border;
    }
  body {
    @apply bg-background text-foreground;
    }
}

/* Theme-aware glassmorphism effects */
@layer components {
  .glassmorphism-header {
    backdrop-filter: blur(16px) saturate(180%);
    -webkit-backdrop-filter: blur(16px) saturate(180%);
    transition: all 0.3s ease;
  }

  /* Light theme glassmorphism */
  :root .glassmorphism-header {
    background: rgba(209, 208, 163, 0.15); /* Light olive with transparency */
    border: 1px solid rgba(124, 108, 119, 0.2); /* Muted purple-gray border */
    box-shadow: 0 8px 32px rgba(91, 48, 0, 0.1); /* Subtle brown shadow */
  }

  /* Dark theme glassmorphism */
  .dark .glassmorphism-header {
    background: rgba(17, 24, 39, 0.25); /* Dark background */
    border: 1px solid rgba(255, 255, 255, 0.125); /* Light border */
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3); /* Dark shadow */
  }

  .glassmorphism-card {
    backdrop-filter: blur(12px) saturate(150%);
    -webkit-backdrop-filter: blur(12px) saturate(150%);
    transition: all 0.3s ease;
  }

  /* Light theme card glassmorphism */
  :root .glassmorphism-card {
    background: rgba(223, 194, 242, 0.1); /* Light purple with transparency */
    border: 1px solid rgba(170, 166, 148, 0.3); /* Warm gray border */
  }

  /* Dark theme card glassmorphism */
  .dark .glassmorphism-card {
    background: rgba(255, 255, 255, 0.1); /* Light background */
    border: 1px solid rgba(255, 255, 255, 0.2); /* Light border */
  }
}
