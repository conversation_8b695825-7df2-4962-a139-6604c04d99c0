{"name": "genrec-backend", "version": "1.0.0", "description": "Backend server for Genrec AI customer data collection", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "postgresql": "node postgresql-server.js", "init-db": "node init-postgresql.js", "quick": "node quick-start.js", "test": "jest", "migrate": "node scripts/migrate.js"}, "keywords": ["genrec", "ai", "backend", "customer-data"], "author": "Genrec AI", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "pg": "^8.11.3", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}