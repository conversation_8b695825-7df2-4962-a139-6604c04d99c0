<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#facc15;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#eab308;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Outer circle -->
  <circle cx="50" cy="50" r="45" fill="none" stroke="url(#logoGradient)" stroke-width="3"/>
  
  <!-- Inner geometric pattern -->
  <polygon points="50,20 70,40 50,60 30,40" fill="url(#logoGradient)" opacity="0.8"/>
  
  <!-- Central circle -->
  <circle cx="50" cy="50" r="15" fill="url(#logoGradient)"/>
  
  <!-- AI circuit pattern -->
  <path d="M25,25 L35,35 M65,25 L75,35 M25,75 L35,65 M65,75 L75,65" 
        stroke="url(#logoGradient)" stroke-width="2" opacity="0.6"/>
  
  <!-- G letter stylized -->
  <path d="M40,50 Q40,35 50,35 Q60,35 60,45 L55,45 M50,50 L60,50 Q60,65 50,65 Q40,65 40,50" 
        fill="none" stroke="#000" stroke-width="2"/>
</svg>
