@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Inter:wght@300;400;500;600;700&display=swap');

.App {
  min-height: 100vh;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Custom font classes */
.font-orbitron {
  font-family: 'Orbitron', monospace;
}

.font-inter {
  font-family: 'Inter', sans-serif;
}

/* Apply fonts globally */
body {
  font-family: 'Inter', sans-serif;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Orbitron', monospace;
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite linear;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
}

.animate-slide-in-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-fade-in-scale {
  animation: fadeInScale 0.5s ease-out;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  /* Reduce animation complexity on mobile */
  .animate-pulse {
    animation-duration: 4s;
    animation-timing-function: ease-in-out;
  }

  .animate-bounce {
    animation-duration: 3s;
  }

  /* Optimize transforms for mobile */
  .hover\:scale-105:hover {
    transform: scale(1.02);
  }

  .hover\:scale-110:hover {
    transform: scale(1.05);
  }

  /* Reduce blur effects on mobile for performance */
  .blur-xl {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
  }

  .blur-lg {
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
  }

  .blur-md {
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
  }

  /* Optimize hero logo animation for mobile */
  .hero-logo-mobile {
    animation-duration: 4s !important;
    animation-timing-function: ease-in-out !important;
  }

  /* Disable complex animations on very small screens */
  @media (max-width: 480px) {
    .animate-pulse {
      animation: none;
    }

    .hero-logo-mobile {
      animation: none;
    }
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Remove hover effects on touch devices */
  .hover\:scale-105:hover,
  .hover\:scale-110:hover,
  .hover\:-translate-y-2:hover {
    transform: none;
  }

  /* Increase touch targets */
  button, .cursor-pointer {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .animate-pulse,
  .animate-bounce,
  .animate-spin {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-gray-900\/50 {
    background-color: rgb(0 0 0 / 0.8);
  }

  .border-gray-800\/50 {
    border-color: rgb(255 255 255 / 0.3);
  }
}

/* Chatbot mobile optimizations */
@media (max-width: 640px) {
  /* Ensure chatbot doesn't overflow on small screens */
  .chatbot-container {
    max-width: calc(100vw - 2rem);
    max-height: calc(100vh - 6rem);
  }

  /* Optimize touch targets for mobile */
  .chatbot-button {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improve text readability on mobile */
  .chatbot-message {
    font-size: 14px;
    line-height: 1.4;
  }

  /* Optimize input area for mobile keyboards */
  .chatbot-input {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Landscape mobile optimizations */
@media (max-width: 768px) and (orientation: landscape) {
  .chatbot-container {
    max-height: calc(100vh - 4rem);
  }
}

/* Tablet optimizations */
@media (min-width: 641px) and (max-width: 1024px) {
  .chatbot-container {
    width: 320px;
    height: 500px;
  }
}

/* Logo visibility optimizations */
.logo-container {
  position: relative;
  z-index: 60;
}

.logo-container img {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.5));
}

/* Mobile-specific logo optimizations */
@media (max-width: 768px) {
  .mobile-logo-optimized {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
  }

  .mobile-logo-text {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  }
}

/* Mobile navbar glassmorphism */
@media (max-width: 768px) {
  .mobile-nav-overlay {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: rgba(0, 0, 0, 0.8);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(17, 24, 39, 0.8) 100%);
  }

  .mobile-nav-item {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
}

/* Brochure responsive optimizations */
@media (max-width: 640px) {
  .brochure-title {
    font-size: 2.5rem;
    line-height: 1.1;
  }

  .brochure-subtitle {
    font-size: 1.125rem;
    line-height: 1.4;
  }

  .brochure-card {
    padding: 1rem;
  }

  .brochure-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  .brochure-industries {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .brochure-title {
    font-size: 3.5rem;
  }

  .brochure-industries {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Enhanced glassmorphism effects */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-effect-dark {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Improved button interactions */
@media (hover: hover) {
  .interactive-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .interactive-button:active {
    transform: scale(0.98);
  }

  .mobile-nav-item:active {
    background: rgba(255, 255, 255, 0.15);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(250, 204, 21, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(250, 204, 21, 0.6);
  }
}

@keyframes glow {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(250, 204, 21, 0.3);
  }
  50% { 
    box-shadow: 0 0 30px rgba(250, 204, 21, 0.5), 0 0 40px rgba(250, 204, 21, 0.3);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #facc15;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #eab308;
}

/* Selection styles */
::selection {
  background: rgba(250, 204, 21, 0.3);
  color: white;
}

/* Custom grid for particle effects */
.grid-cols-20 {
  grid-template-columns: repeat(20, minmax(0, 1fr));
}

.grid-rows-20 {
  grid-template-rows: repeat(20, minmax(0, 1fr));
}

/* Glassmorphism effect */
.glassmorphism {
  background: rgba(17, 24, 39, 0.25);
  backdrop-filter: blur(16px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.125);
}

/* Hover effects for cards */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Custom gradient text */
.gradient-text {
  background: linear-gradient(135deg, #facc15 0%, #eab308 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Loading spinner */
@keyframes spin {
  to { transform: rotate(360deg); }
}

.animate-spin-slow {
  animation: spin 3s linear infinite;
}

.animate-slide-up {
  animation: slideInUp 0.6s ease-out forwards;
}

.animate-slide-left {
  animation: slideInLeft 0.6s ease-out forwards;
}

.animate-slide-right {
  animation: slideInRight 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out forwards;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Staggered animation delays */
.animate-delay-100 {
  animation-delay: 0.1s;
}

.animate-delay-200 {
  animation-delay: 0.2s;
}

.animate-delay-300 {
  animation-delay: 0.3s;
}

.animate-delay-400 {
  animation-delay: 0.4s;
}

/* Focus styles for accessibility */
button:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible {
  outline: 2px solid #facc15;
  outline-offset: 2px;
}

/* Custom button hover effects */
.btn-glow:hover {
  box-shadow: 0 0 20px rgba(250, 204, 21, 0.4);
}

/* Responsive typography */
@media (max-width: 768px) {
  .text-responsive {
    font-size: clamp(1.5rem, 4vw, 3rem);
  }
}

/* Theme compatibility - handled by CSS custom properties */

/* Chatbot animations */
@keyframes slideInFromBottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-in {
  animation: slideInFromBottom 0.3s ease-out;
}

.slide-in-from-bottom-4 {
  animation: slideInFromBottom 0.3s ease-out;
}

/* Chatbot message animations */
.message-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.message-scale-in {
  animation: scaleIn 0.2s ease-out;
}

/* Floating button pulse effect */
.chatbot-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .8;
    transform: scale(1.05);
  }
}