# Environment Configuration
NODE_ENV=development
PORT=5000

# Supabase Configuration
SUPABASE_URL=https://liudfsttbkzfchsgovaj.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxpdWRmc3R0Ymt6ZmNoc2dvdmFqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTUwNzQwMzMsImV4cCI6MjA3MDY1MDAzM30.x916YmKm-5QZ4kuCLSqmbHxI3_sQ5yTRWzWNvfk_4DY

# Frontend URL (for CORS in production)
FRONTEND_URL=http://localhost:5000

# React App Environment Variables
REACT_APP_BACKEND_URL=http://localhost:5000
REACT_APP_SUPABASE_URL=https://liudfsttbkzfchsgovaj.supabase.co
REACT_APP_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxpdWRmc3R0Ymt6ZmNoc2dvdmFqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTUwNzQwMzMsImV4cCI6MjA3MDY1MDAzM30.x916YmKm-5QZ4kuCLSqmbHxI3_sQ5yTRWzWNvfk_4DY

# PostHog Analytics (optional)
# REACT_APP_POSTHOG_KEY=your_posthog_key
# REACT_APP_POSTHOG_HOST=https://us.i.posthog.com
