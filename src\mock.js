// Mock data for Genrec AI website

export const mockData = {
  // Hero Section
  hero: {
    title: "GENREC AI",
    subtitle: "We develop practical AI solutions for modern businesses.",
    tagline: "Building Intelligent Software Solutions",
    stats: [
      { number: "10+", label: "Projects Completed" },
      { number: "3+", label: "Industries Served" },
      { number: "88%", label: "Client Satisfaction" },
      { number: "2+", label: "Years Experience" }
    ]
  },

  // About Section
  about: {
    title: "Building Better Software",
    description: "Genrec AI specializes in developing practical artificial intelligence solutions for businesses. We focus on creating reliable, scalable software that integrates seamlessly with existing workflows and delivers measurable results.",
    features: [
      {
        title: "Data Security Focus",
        description: "We implement robust security measures to protect your data throughout the development process.",
        icon: "shield-check"
      },
      {
        title: "Complete Project Management",
        description: "From initial consultation to final deployment, we manage every phase of your project.",
        icon: "zap"
      },
      {
        title: "Scalable Architecture",
        description: "Our solutions are designed to grow with your business needs and handle increased demand.",
        icon: "trending-up"
      },
      {
        title: "Ongoing Support",
        description: "We provide maintenance and support services to ensure your systems continue running smoothly.",
        icon: "cpu"
      }
    ],
    stats: [
      { number: "8+", label: "Projects Delivered" },
      { number: "3", label: "Active Clients" },
      { number: "88%", label: "Client Retention" },
      { number: "2+", label: "Years Experience" }
    ]
  },

  // Services Section
  services: [
    {
      title: "AI-Enhanced Websites",
      description: "Modern websites with intelligent features including dynamic content, interactive elements, and responsive design optimized for user engagement.",
      features: ["Responsive Design", "Dynamic Content", "Interactive Elements", "Performance Optimization"],
      icon: "globe"
    },
    {
      title: "Business Management Systems",
      description: "Custom CRM and business management solutions with analytics dashboards and workflow automation to streamline operations.",
      features: ["Customer Management", "Analytics Dashboards", "Workflow Automation", "Data Integration"],
      icon: "users"
    },
    {
      title: "Data Science MVPs",
      description: "Rapid prototyping of machine learning solutions with custom pipelines and predictive models.",
      features: ["Custom ML Models", "Data Pipelines", "Predictive Analytics", "Real-time Processing"],
      icon: "brain"
    },
    {
      title: "AI-Powered Mobile Apps",
      description: "React Native and Flutter applications with sensor integration and offline-first architecture.",
      features: ["Cross-Platform", "Sensor Integration", "Offline Sync", "Push Notifications"],
      icon: "smartphone"
    },
    {
      title: "Web Services & APIs",
      description: "Scalable microservices architecture with REST/GraphQL APIs and Kubernetes deployment.",
      features: ["Microservices", "Auto-Scaling", "API Gateway", "Monitoring"],
      icon: "server"
    },
    {
      title: "Zen Analyzer",
      description: "Upload CSV/Excel files and get AI-driven insights, summaries, and interactive Q&A capabilities.",
      features: ["Natural Language Queries", "Chart Generation", "Data Export", "Interactive Analysis"],
      icon: "file-text"
    },
    {
      title: "Smart E-commerce",
      description: "Headless storefronts with AI personalization, AR product previews, and intelligent recommendations.",
      features: ["AI Personalization", "AR Previews", "Smart Recommendations", "Headless Architecture"],
      icon: "shopping-cart"
    },
    {
      title: "Educational AI",
      description: "Adaptive learning platforms with AI tutors, anonymous feedback systems, and progress tracking.",
      features: ["Adaptive Learning", "AI Tutoring", "Progress Analytics", "Anonymous Feedback"],
      icon: "graduation-cap"
    }
  ],

  // Projects Section
  projects: [
    {
      id: 1,
      title: "LuminaIQ",
      category: "EdTech",
      description: "AI-powered learning platform with adaptive question generation and real-time performance evaluation.",
      image: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=600&h=400&fit=crop",
      tags: ["AI", "Education", "React", "Python"],
      highlights: ["Adaptive Quiz Generation", "Real-time Feedback", "Gamified Progress", "Multi-language Support"],
      metrics: {
        users: "10K+",
        accuracy: "94%",
        engagement: "85%"
      },
      demoUrl: "#",
      caseStudyUrl: "#"
    },
    {
      id: 2,
      title: "CRM Pro Suite",
      category: "Business",
      description: "Comprehensive CRM solution with AI-powered analytics, billing management, and investment tracking.",
      image: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=400&fit=crop",
      tags: ["CRM", "Analytics", "Dashboard", "AI"],
      highlights: ["Drag-Drop Reporting", "Role-based Permissions", "Audit Logs", "Predictive Analytics"],
      metrics: {
        efficiency: "60%",
        satisfaction: "98%",
        roi: "300%"
      },
      demoUrl: "#",
      caseStudyUrl: "#"
    },
    {
      id: 3,
      title: "DataForge ML",
      category: "Data Science",
      description: "End-to-end machine learning pipeline with automated model selection and deployment capabilities.",
      image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&h=400&fit=crop",
      tags: ["ML", "Python", "Docker", "Kubernetes"],
      highlights: ["AutoML Capabilities", "Model Deployment", "Data Visualization", "API Integration"],
      metrics: {
        accuracy: "96%",
        speed: "10x",
        scalability: "∞"
      },
      demoUrl: "#",
      caseStudyUrl: "#"
    },
    {
      id: 4,
      title: "Tabble Manager",
      category: "Mobile",
      description: "Sensor-driven hotel table management system with real-time occupancy tracking and analytics.",
      image: "https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=600&h=400&fit=crop",
      tags: ["React Native", "IoT", "Real-time", "Analytics"],
      highlights: ["Sensor Integration", "Real-time Updates", "Offline Sync", "Analytics Dashboard"],
      metrics: {
        efficiency: "40%",
        accuracy: "99%",
        uptime: "99.9%"
      },
      demoUrl: "#",
      caseStudyUrl: "#"
    },
    {
      id: 5,
      title: "Zen Analyzer Pro",
      category: "Data Analysis",
      description: "AI-powered data analysis tool that transforms CSV/Excel files into interactive insights and visualizations.",
      image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&h=400&fit=crop",
      tags: ["AI", "Analytics", "Visualization", "NLP"],
      highlights: ["Natural Language Queries", "Auto-Visualization", "Export Capabilities", "Multi-format Support"],
      metrics: {
        speed: "50x",
        accuracy: "95%",
        satisfaction: "96%"
      },
      demoUrl: "#",
      caseStudyUrl: "#"
    },
    {
      id: 6,
      title: "EduTech Suite",
      category: "Education",
      description: "Comprehensive educational platform with anonymous feedback system and AI-powered learning analytics.",
      image: "https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=600&h=400&fit=crop",
      tags: ["Education", "React", "AI", "Analytics"],
      highlights: ["Anonymous Feedback", "Learning Analytics", "Progress Tracking", "Multi-tenant Support"],
      metrics: {
        adoption: "89%",
        engagement: "76%",
        retention: "92%"
      },
      demoUrl: "#",
      caseStudyUrl: "#"
    }
  ],

  // Contact Information
  contact: {
    email: "<EMAIL>",
    address: "Karur, Tamil Nadu",
    social: {
      github: "https://github.com/Genrec-Community"
    }
  },

  // Navigation
  navigation: [
    { label: "Home", href: "#home" },
    { label: "About", href: "#about" },
    { label: "Services", href: "#services" },
    { label: "Projects", href: "#projects" },
    { label: "Contact", href: "#contact" }
  ]
};