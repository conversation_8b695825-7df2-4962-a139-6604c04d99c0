import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from './ui/button';
import { Menu, X } from 'lucide-react';
import ThemeSwitcher from './ThemeSwitcher';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navigation = [
    { label: "Home", href: "/" },
    { label: "About", href: "/about" },
    { label: "Services", href: "/services" },
    { label: "Projects", href: "/projects" },
    { label: "Contact", href: "/contact" }
  ];

  const handleGetStarted = () => {
    navigate('/contact');
  };

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      scrolled
        ? 'glassmorphism-header border-b border-border/50'
        : 'bg-transparent'
    }`}>
      <div className="container mx-auto px-6 py-1">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-3 relative z-50">
            <div className="relative">
              <img
                src="/Genrec_Mini_Logo.png"
                alt="Genrec AI"
                className="w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20 lg:w-24 lg:h-24 relative z-10 filter drop-shadow-lg"
              />
            </div>
            <span className="text-xl sm:text-2xl font-bold text-foreground font-orbitron filter drop-shadow-lg">GENREC</span>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            {navigation.map((item) => (
              <Link
                key={item.label}
                to={item.href}
                className="text-muted-foreground hover:text-foreground transition-colors duration-200 text-sm font-medium"
              >
                {item.label}
              </Link>
            ))}
            <ThemeSwitcher className="mx-2" />
            <Button
              onClick={handleGetStarted}
              className="bg-primary/10 border border-primary/30 text-primary hover:bg-primary/20 hover:text-primary-foreground transition-all duration-200"
            >
              Get Started
            </Button>
          </nav>

          {/* Mobile Menu Button and Theme Switcher */}
          <div className="md:hidden flex items-center space-x-2">
            <ThemeSwitcher />
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-foreground p-2"
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden fixed inset-0 top-20 glassmorphism-header z-40 animate-in fade-in-0 duration-300">
            <div className="absolute inset-0 bg-background/20"></div>
            <nav className="relative z-10 p-6">
              <div className="flex flex-col space-y-6">
                {navigation.map((item) => (
                  <Link
                    key={item.label}
                    to={item.href}
                    onClick={() => setIsMenuOpen(false)}
                    className="text-muted-foreground hover:text-foreground transition-all duration-300 text-lg font-medium py-3 px-4 rounded-lg hover:bg-accent/20 backdrop-blur-sm border border-transparent hover:border-border/30 transform hover:scale-105"
                  >
                    {item.label}
                  </Link>
                ))}
                <Button
                  onClick={() => {
                    handleGetStarted();
                    setIsMenuOpen(false);
                  }}
                  className="bg-primary/20 border border-primary/40 text-primary hover:bg-primary/30 hover:text-primary-foreground transition-all duration-300 w-full mt-6 py-3 backdrop-blur-sm hover:scale-105 shadow-lg"
                >
                  Get Started
                </Button>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;